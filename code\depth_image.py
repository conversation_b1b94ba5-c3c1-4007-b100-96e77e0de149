import cv2 as cv
from transformers.pipelines import pipeline
from PIL import Image
import numpy as np

pipe = pipeline(task="depth-estimation", model="depth-anything/Depth-Anything-V2-Small-hf")


def get_depth_image(image: cv.typing.MatLike) -> cv.typing.MatLike:
    return np.asarray(pipe(Image.fromarray(image))["depth"])  # type: ignore[call-arg]


def get_edges_from_depth_image(image: cv.typing.MatLike) -> cv.typing.MatLike:
    edges = cv.<PERSON>ny(image, 2000, 3000, apertureSize=7)
    amount_of_edge_pixels = np.count_nonzero(edges)
    if amount_of_edge_pixels < 4500:
        # on a lot of small objects we don't want to dilate as the image gets too filled
        edges = cv.dilate(edges, np.ones((3, 3), np.uint8), iterations=1)
    return edges


if __name__ == "__main__":
    image = cv.imread("./data/depth_image.png")
    edges = get_edges_from_depth_image(image)
    print(edges)
    cv.imshow("edges", edges)
    cv.waitKey(0)
